const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const Database = require('./database');

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

let mainWindow;
let db;
let currentUser = null; // Store current logged-in user

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    center: true, // Center the window on screen
    resizable: true,
    maximizable: true, // Enable maximize functionality
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    titleBarStyle: 'hidden', // Remove default toolbar
    frame: false, // Remove window frame for modern look
    show: false // Don't show window until ready (prevents console opening)
  });

  // Load the welcome page initially
  mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

  // Show window when ready to prevent console opening
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Initialize database
  db = new Database();
  db.init();
}

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC handlers for login
ipcMain.handle('login', async (event, credentials) => {
  try {
    const user = await db.authenticateUser(credentials.username, credentials.password, credentials.role);
    if (user) {
      // Store current user information
      currentUser = user;

      // Load user permissions if not Admin
      let permissions = [];
      if (user.role !== 'Admin') {
        try {
          permissions = await db.getUserPermissions(user.id);
          console.log(`Loaded ${permissions.length} permissions for user ${user.username}`);
        } catch (permError) {
          console.error('Error loading user permissions:', permError);
        }
      }

      // Store permissions with user
      currentUser.permissions = permissions;

      // Automatically maximize window for application interfaces
      if (mainWindow && !mainWindow.isMaximized()) {
        mainWindow.maximize();
      }

      // Determine which interface to load based on user permissions
      const redirectPage = determineUserInterface(permissions, user.role);
      console.log(`Redirecting user ${user.username} (${user.role}) to: ${redirectPage}`);

      // Load the appropriate interface
      const pagePath = redirectPage === 'pos' ? 'pages/pos.html' :
                      redirectPage === 'admin' ? 'pages/admin.html' :
                      'pages/theater.html';

      mainWindow.loadFile(path.join(__dirname, pagePath));
      return { success: true, user: currentUser };
    } else {
      return { success: false, message: 'Invalid credentials' };
    }
  } catch (error) {
    return { success: false, message: 'Database error' };
  }
});

// Determine which interface to load based on user permissions
function determineUserInterface(permissions, userRole) {
  // Admin always goes to POS (can access everything)
  if (userRole === 'Admin') {
    return 'pos';
  }

  // For non-admin users, analyze their permissions
  if (!permissions || permissions.length === 0) {
    console.warn('User has no permissions, defaulting to POS');
    return 'pos';
  }

  const hasPos = permissions.some(p => p.module_id === 'pos');
  const hasTheater = permissions.some(p => p.module_id === 'theater');
  const hasAdmin = permissions.some(p =>
    p.module_id === 'dashboard' ||
    p.module_id === 'master' ||
    p.module_id === 'reports' ||
    p.module_id === 'transactions' ||
    p.module_id === 'wholesale' ||
    p.module_id === 'user-management'
  );

  console.log('Permission analysis:', { hasPos, hasTheater, hasAdmin });

  // Priority logic for redirection
  if (hasTheater && !hasPos && !hasAdmin) {
    // Only theater access - go directly to theater
    return 'theater';
  } else if (hasAdmin && !hasPos && !hasTheater) {
    // Only admin access - go directly to admin
    return 'admin';
  } else if (hasPos) {
    // Has POS access (with or without others) - go to POS
    return 'pos';
  } else {
    // Fallback to POS if unclear
    console.warn('Unclear permissions, defaulting to POS');
    return 'pos';
  }
}

// Get current user information
ipcMain.handle('get-current-user', () => {
  return currentUser;
});

// Logout handler
ipcMain.handle('logout', async () => {
  try {
    // Clear current user session
    currentUser = null;

    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }

    // Reset window to original login state (fixed size, centered)
    if (mainWindow) {
      // Unmaximize if currently maximized
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize();
      }

      // Reset to original dimensions and center
      mainWindow.setSize(1200, 800);
      mainWindow.center();
    }

    // Navigate to welcome page instead of login page
    await mainWindow.loadFile(path.join(__dirname, 'welcome.html'));

    // Wait for welcome page to load
    await new Promise(resolve => {
      mainWindow.webContents.once('did-finish-load', () => {
        resolve();
      });
    });

    return { success: true };
  } catch (error) {
    console.error('Logout error:', error);
    return { success: false, message: 'Logout failed' };
  }
});

// IPC handlers for navigation
ipcMain.handle('navigate-to', async (event, page) => {
  try {
    switch (page) {
      case 'pos':
        // Automatically maximize window for POS interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'pos.html'));
        break;
      case 'admin':
        // Automatically maximize window for admin interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'admin.html'));
        break;
      case 'theater':
        // Automatically maximize window for theater interface
        if (mainWindow && !mainWindow.isMaximized()) {
          mainWindow.maximize();
        }
        mainWindow.loadFile(path.join(__dirname, 'pages', 'theater.html'));
        break;
      case 'welcome':
        // Reset window to original state when navigating to welcome
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'welcome.html'));
        break;
      case 'login':
        // Reset window to original login state when navigating to login
        if (mainWindow) {
          // Unmaximize if currently maximized
          if (mainWindow.isMaximized()) {
            mainWindow.unmaximize();
          }

          // Reset to original dimensions and center
          mainWindow.setSize(1200, 800);
          mainWindow.center();
        }
        mainWindow.loadFile(path.join(__dirname, 'login.html'));
        break;
      default:
        console.log('Unknown page:', page);
    }
  } catch (error) {
    console.error('Navigation error:', error);
  }
});

// IPC handler for navigating to specific page files
ipcMain.handle('navigate-to-page', async (event, pagePath) => {
  try {
    const fullPath = path.join(__dirname, 'pages', pagePath);
    console.log('Navigating to page:', fullPath);
    mainWindow.loadFile(fullPath);
    return { success: true };
  } catch (error) {
    console.error('Page navigation error:', error);
    return { success: false, message: error.message };
  }
});

// IPC handler for navigating to admin panel sections
ipcMain.handle('navigate-to-admin-section', async (event, section) => {
  try {
    const adminPath = path.join(__dirname, 'pages', 'admin.html');
    console.log('Navigating to admin section:', section);

    // Load admin page first
    await mainWindow.loadFile(adminPath);

    // Wait a bit for the page to load, then trigger the section
    setTimeout(() => {
      mainWindow.webContents.executeJavaScript(`
        if (typeof handleNavClick === 'function') {
          handleNavClick('${section}');
        }
      `);
    }, 500);

    return { success: true };
  } catch (error) {
    console.error('Admin section navigation error:', error);
    return { success: false, message: error.message };
  }
});

// Handle window controls
ipcMain.handle('close-app', () => {
  app.quit();
});

ipcMain.handle('minimize-app', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('toggle-fullscreen', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
      return false;
    } else {
      mainWindow.maximize();
      return true;
    }
  }
  return false;
});

ipcMain.handle('is-fullscreen', () => {
  if (mainWindow) {
    return mainWindow.isMaximized();
  }
  return false;
});

// User Management IPC handlers
ipcMain.handle('create-user', async (event, userData) => {
  try {
    const result = await db.createUser(userData);
    return { success: true, user: result };
  } catch (error) {
    console.error('Error creating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-users', async () => {
  try {
    const users = await db.getAllUsers();
    return { success: true, users };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-by-id', async (event, id) => {
  try {
    const user = await db.getUserById(id);
    return { success: true, user };
  } catch (error) {
    console.error('Error fetching user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user', async (event, id, userData) => {
  try {
    const result = await db.updateUser(id, userData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating user:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-user-password', async (event, id, newPassword) => {
  try {
    const result = await db.updateUserPassword(id, newPassword);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating password:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-user', async (event, id) => {
  try {
    const result = await db.deleteUser(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting user:', error);
    return { success: false, message: error.message };
  }
});

// Permission Management IPC handlers
ipcMain.handle('create-user-permissions', async (event, userId, permissions) => {
  try {
    const result = await db.createUserPermissions(userId, permissions);
    return { success: true, result };
  } catch (error) {
    console.error('Error creating user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-user-permissions', async (event, userId) => {
  try {
    const permissions = await db.getUserPermissions(userId);
    return { success: true, permissions };
  } catch (error) {
    console.error('Error fetching user permissions:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('force-create-permissions-table', async () => {
  try {
    await db.forceCreatePermissionsTable();
    return { success: true };
  } catch (error) {
    console.error('Error force creating permissions table:', error);
    return { success: false, message: error.message };
  }
});

// Debug handler to check database directly
ipcMain.handle('debug-user-permissions', async (event, username) => {
  try {
    // Get user by username
    const user = await db.getUserByUsername(username);
    if (!user) {
      return { success: false, message: 'User not found' };
    }

    // Get permissions
    const permissions = await db.getUserPermissions(user.id);

    return {
      success: true,
      user: user,
      permissions: permissions
    };
  } catch (error) {
    console.error('Error debugging user permissions:', error);
    return { success: false, message: error.message };
  }
});

// Product Management IPC handlers
ipcMain.handle('create-product', async (event, productData, locationStocks) => {
  try {
    const result = await db.createProduct(productData);

    // Create location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.createLocationStocks(result.id, locationStocks);
    }

    return { success: true, product: result };
  } catch (error) {
    console.error('Error creating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-products', async () => {
  try {
    const products = await db.getAllProducts();
    return { success: true, products };
  } catch (error) {
    console.error('Error fetching products:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-id', async (event, id) => {
  try {
    const product = await db.getProductById(id);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-product-by-barcode', async (event, barcode) => {
  try {
    const product = await db.getProductByBarcode(barcode);
    let locationStocks = [];

    if (product) {
      locationStocks = await db.getLocationStocksByProductId(product.id);
    }

    return { success: true, product, locationStocks };
  } catch (error) {
    console.error('Error fetching product by barcode:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-product', async (event, id, productData, locationStocks) => {
  try {
    const result = await db.updateProduct(id, productData);

    // Update location stocks if provided
    if (locationStocks && locationStocks.length > 0) {
      await db.updateLocationStocks(id, locationStocks);
    }

    return { success: true, result };
  } catch (error) {
    console.error('Error updating product:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-product', async (event, id) => {
  try {
    const result = await db.deleteProduct(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting product:', error);
    return { success: false, message: error.message };
  }
});

// Image upload handler for products
ipcMain.handle('save-product-image', async (event, fileData, barcode) => {
  try {
    // Create images directory if it doesn't exist
    const imagesDir = path.join(__dirname, 'images', 'products');
    if (!fs.existsSync(imagesDir)) {
      fs.mkdirSync(imagesDir, { recursive: true });
    }

    // Generate unique filename using barcode and timestamp
    const timestamp = Date.now();
    const fileExtension = path.extname(fileData.name);
    const fileName = `${barcode}_${timestamp}${fileExtension}`;
    const imagePath = path.join(imagesDir, fileName);

    // Convert base64 data to buffer and save
    const base64Data = fileData.data.replace(/^data:image\/[a-z]+;base64,/, '');
    const buffer = Buffer.from(base64Data, 'base64');
    fs.writeFileSync(imagePath, buffer);

    console.log('Image saved successfully:', imagePath);
    return { success: true, imagePath };
  } catch (error) {
    console.error('Error saving product image:', error);
    return { success: false, message: error.message };
  }
});

// Category Management IPC handlers
ipcMain.handle('create-category', async (event, categoryData) => {
  try {
    const result = await db.createCategory(categoryData);
    return { success: true, category: result };
  } catch (error) {
    console.error('Error creating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-id', async (event, id) => {
  try {
    const category = await db.getCategoryById(id);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-category-by-code', async (event, code) => {
  try {
    const category = await db.getCategoryByCode(code);
    return { success: true, category };
  } catch (error) {
    console.error('Error fetching category by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-category', async (event, id, categoryData) => {
  try {
    const result = await db.updateCategory(id, categoryData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-category', async (event, id) => {
  try {
    const result = await db.deleteCategory(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting category:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-parent-categories', async () => {
  try {
    const categories = await db.getParentCategories();
    return { success: true, categories };
  } catch (error) {
    console.error('Error fetching parent categories:', error);
    return { success: false, message: error.message };
  }
});

// Supplier Management IPC handlers
ipcMain.handle('create-supplier', async (event, supplierData) => {
  try {
    const result = await db.createSupplier(supplierData);
    return { success: true, supplier: result };
  } catch (error) {
    console.error('Error creating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-suppliers', async () => {
  try {
    const suppliers = await db.getAllSuppliers();
    return { success: true, suppliers };
  } catch (error) {
    console.error('Error fetching suppliers:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-id', async (event, id) => {
  try {
    const supplier = await db.getSupplierById(id);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-supplier-by-name', async (event, name) => {
  try {
    const supplier = await db.getSupplierByName(name);
    return { success: true, supplier };
  } catch (error) {
    console.error('Error fetching supplier by name:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-supplier', async (event, id, supplierData) => {
  try {
    const result = await db.updateSupplier(id, supplierData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating supplier:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-supplier', async (event, id) => {
  try {
    const result = await db.deleteSupplier(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting supplier:', error);
    return { success: false, message: error.message };
  }
});

// Location Management IPC handlers
ipcMain.handle('create-location', async (event, locationData) => {
  try {
    const result = await db.createLocation(locationData);
    return { success: true, location: result };
  } catch (error) {
    console.error('Error creating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-all-locations', async () => {
  try {
    const locations = await db.getAllLocations();
    return { success: true, locations };
  } catch (error) {
    console.error('Error fetching locations:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-id', async (event, id) => {
  try {
    const location = await db.getLocationById(id);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-location-by-code', async (event, location_code) => {
  try {
    const location = await db.getLocationByCode(location_code);
    return { success: true, location };
  } catch (error) {
    console.error('Error fetching location by code:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('update-location', async (event, id, locationData) => {
  try {
    const result = await db.updateLocation(id, locationData);
    return { success: true, result };
  } catch (error) {
    console.error('Error updating location:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('delete-location', async (event, id) => {
  try {
    const result = await db.deleteLocation(id);
    return { success: true, result };
  } catch (error) {
    console.error('Error deleting location:', error);
    return { success: false, message: error.message };
  }
});

// POS System IPC handlers for categories and products
ipcMain.handle('get-pos-categories', async () => {
  try {
    const categories = await db.getAllCategories();
    // Return only parent categories for main category filter
    const parentCategories = categories.filter(cat => !cat.parent_id && cat.status === 'active');
    return { success: true, categories: parentCategories };
  } catch (error) {
    console.error('Error fetching POS categories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-subcategories', async (event, parentCategoryId) => {
  try {
    const categories = await db.getAllCategories();
    // Return subcategories for the selected parent category
    const subcategories = categories.filter(cat =>
      cat.parent_id === parentCategoryId && cat.status === 'active'
    );
    return { success: true, subcategories };
  } catch (error) {
    console.error('Error fetching POS subcategories:', error);
    return { success: false, message: error.message };
  }
});

ipcMain.handle('get-pos-products', async () => {
  try {
    const products = await db.getAllProducts();
    // Get categories and suppliers to populate product details
    const categories = await db.getAllCategories();
    const suppliers = await db.getAllSuppliers();

    // Map products with category and supplier names
    const enrichedProducts = products.map(product => {
      const category = categories.find(c => c.name === product.category);
      const supplier = suppliers.find(s => s.name === product.supplier);

      return {
        id: product.id.toString(),
        name: product.description,
        price: parseFloat(product.purchase_price) * 1.5, // Apply markup
        category: product.category,
        subcategory: product.subcategory,
        supplier: product.supplier,
        barcode: product.barcode,
        image: product.image_path || `https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=${encodeURIComponent(product.description.substring(0, 10))}`
      };
    });

    return { success: true, products: enrichedProducts };
  } catch (error) {
    console.error('Error fetching POS products:', error);
    return { success: false, message: error.message };
  }
});
